#!/usr/bin/env python3
"""
سكريبت لتثبيت وتفعيل Continue.dev في Windsurf IDE
"""

import os
import subprocess
import json
import requests
from pathlib import Path
import shutil

def check_windsurf_installation():
    """التحقق من تثبيت Windsurf"""
    
    print("🔍 البحث عن Windsurf IDE...")
    
    # مسارات محتملة لـ Windsurf
    possible_paths = [
        "/usr/bin/windsurf",
        "/usr/local/bin/windsurf",
        "/opt/windsurf/bin/windsurf",
        "/snap/bin/windsurf",
        os.path.expanduser("~/.local/bin/windsurf"),
        "/Applications/Windsurf.app/Contents/MacOS/Windsurf",  # macOS
    ]
    
    windsurf_path = None
    for path in possible_paths:
        if os.path.exists(path):
            windsurf_path = path
            break
    
    if windsurf_path:
        print(f"✅ تم العثور على Windsurf: {windsurf_path}")
        return windsurf_path
    else:
        # محاولة البحث باستخدام which
        try:
            result = subprocess.run(['which', 'windsurf'], capture_output=True, text=True)
            if result.returncode == 0:
                windsurf_path = result.stdout.strip()
                print(f"✅ تم العثور على Windsurf: {windsurf_path}")
                return windsurf_path
        except:
            pass
        
        print("❌ لم يتم العثور على Windsurf IDE")
        return None

def check_continue_extension():
    """التحقق من تثبيت Continue.dev extension"""
    
    print("\n🔍 التحقق من تثبيت Continue.dev extension...")
    
    # مسارات extensions لـ VS Code/Windsurf
    possible_extension_dirs = [
        os.path.expanduser("~/.vscode/extensions"),
        os.path.expanduser("~/.vscode-server/extensions"),
        os.path.expanduser("~/.windsurf/extensions"),
        os.path.expanduser("~/.config/Code/User/extensions"),
        os.path.expanduser("~/.config/windsurf/extensions"),
    ]
    
    continue_extension = None
    for ext_dir in possible_extension_dirs:
        if os.path.exists(ext_dir):
            print(f"📁 فحص مجلد: {ext_dir}")
            for item in os.listdir(ext_dir):
                if "continue" in item.lower():
                    continue_extension = os.path.join(ext_dir, item)
                    print(f"✅ تم العثور على Continue.dev: {continue_extension}")
                    return continue_extension
    
    print("❌ Continue.dev extension غير مثبت")
    return None

def install_continue_extension():
    """تثبيت Continue.dev extension"""
    
    print("\n📦 تثبيت Continue.dev extension...")
    
    # محاولة التثبيت باستخدام أوامر مختلفة
    install_commands = [
        ["code", "--install-extension", "Continue.continue"],
        ["windsurf", "--install-extension", "Continue.continue"],
        ["code-insiders", "--install-extension", "Continue.continue"],
    ]
    
    for cmd in install_commands:
        try:
            print(f"🔄 محاولة: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("✅ تم تثبيت Continue.dev بنجاح!")
                return True
            else:
                print(f"⚠️ فشل: {result.stderr}")
        except subprocess.TimeoutExpired:
            print("⏰ انتهت مهلة التثبيت")
        except FileNotFoundError:
            print(f"❌ الأمر غير موجود: {cmd[0]}")
        except Exception as e:
            print(f"❌ خطأ: {e}")
    
    print("❌ فشل في تثبيت Continue.dev تلقائياً")
    return False

def manual_installation_guide():
    """دليل التثبيت اليدوي"""
    
    print("\n📖 دليل التثبيت اليدوي:")
    print("=" * 50)
    
    print("\n1️⃣ تثبيت Continue.dev في Windsurf:")
    print("   • افتح Windsurf IDE")
    print("   • اضغط Ctrl+Shift+X (أو Cmd+Shift+X على Mac)")
    print("   • ابحث عن 'Continue'")
    print("   • اختر 'Continue - Codestral, Claude, and more'")
    print("   • اضغط 'Install'")
    
    print("\n2️⃣ إذا لم تجد Continue.dev في Marketplace:")
    print("   • حمل ملف .vsix من: https://github.com/continuedev/continue/releases")
    print("   • في Windsurf: View > Command Palette")
    print("   • اكتب 'Extensions: Install from VSIX'")
    print("   • اختر الملف المحمل")
    
    print("\n3️⃣ تفعيل Continue.dev:")
    print("   • أعد تشغيل Windsurf")
    print("   • اضغط Ctrl+I أو Ctrl+Shift+P")
    print("   • ابحث عن 'Continue'")

def create_windsurf_config():
    """إنشاء تكوين خاص بـ Windsurf"""
    
    print("\n⚙️ إنشاء تكوين Windsurf...")
    
    # مسارات تكوين محتملة لـ Windsurf
    config_dirs = [
        os.path.expanduser("~/.windsurf"),
        os.path.expanduser("~/.config/windsurf"),
        os.path.expanduser("~/.windsurf-server"),
    ]
    
    # إنشاء مجلدات التكوين إذا لم تكن موجودة
    for config_dir in config_dirs:
        os.makedirs(config_dir, exist_ok=True)
        
        # إنشاء ملف settings.json
        settings_path = os.path.join(config_dir, "settings.json")
        if not os.path.exists(settings_path):
            settings = {
                "continue.telemetryEnabled": False,
                "continue.enableTabAutocomplete": True,
                "continue.enableCodeLens": True,
                "continue.manuallyTriggerCompletion": False
            }
            
            with open(settings_path, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
            
            print(f"✅ تم إنشاء إعدادات Windsurf: {settings_path}")

def test_windsurf_continue():
    """اختبار Continue.dev في Windsurf"""
    
    print("\n🧪 اختبار Continue.dev في Windsurf...")
    
    # التحقق من ملف التكوين
    continue_config = os.path.expanduser("~/.continue/config.json")
    if os.path.exists(continue_config):
        print("✅ ملف تكوين Continue.dev موجود")
        
        # قراءة التكوين
        try:
            with open(continue_config, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            models = config.get("models", [])
            if models:
                print(f"✅ النماذج مكونة: {len(models)} نموذج")
                
                # اختبار الاتصال مع الخادم
                try:
                    response = requests.get("http://localhost:8001/v1/models", timeout=5)
                    if response.status_code == 200:
                        print("✅ الخادم المحلي يعمل")
                        return True
                    else:
                        print(f"❌ خطأ في الخادم: {response.status_code}")
                except:
                    print("❌ الخادم المحلي لا يعمل")
            else:
                print("❌ لا توجد نماذج مكونة")
        except Exception as e:
            print(f"❌ خطأ في قراءة التكوين: {e}")
    else:
        print("❌ ملف تكوين Continue.dev غير موجود")
    
    return False

def create_windsurf_launcher():
    """إنشاء سكريبت لتشغيل Windsurf مع Continue.dev"""
    
    script_content = """#!/bin/bash
# سكريبت تشغيل Windsurf مع Continue.dev

echo "🚀 بدء تشغيل Windsurf مع Continue.dev..."

# التحقق من الخادم المحلي
if ! curl -s http://localhost:8001/v1/models > /dev/null; then
    echo "⚠️ الخادم المحلي لا يعمل على المنفذ 8001"
    echo "🔧 تأكد من تشغيل خادم CodeGeeX2-6B"
    echo "💡 استخدم: ~/start_codegeex_server.sh"
fi

# تشغيل Windsurf
if command -v windsurf &> /dev/null; then
    echo "✅ تشغيل Windsurf..."
    windsurf "$@"
elif command -v code &> /dev/null; then
    echo "✅ تشغيل VS Code..."
    code "$@"
else
    echo "❌ لم يتم العثور على Windsurf أو VS Code"
    exit 1
fi
"""
    
    script_path = os.path.expanduser("~/start_windsurf_continue.sh")
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    # جعل السكريبت قابل للتنفيذ
    os.chmod(script_path, 0o755)
    print(f"✅ تم إنشاء سكريبت Windsurf: {script_path}")

def main():
    """الدالة الرئيسية"""
    
    print("🔧 إعداد Continue.dev لـ Windsurf IDE")
    print("=" * 50)
    
    # 1. التحقق من Windsurf
    windsurf_path = check_windsurf_installation()
    
    # 2. التحقق من Continue.dev extension
    continue_ext = check_continue_extension()
    
    # 3. محاولة التثبيت إذا لم يكن موجوداً
    if not continue_ext:
        success = install_continue_extension()
        if not success:
            manual_installation_guide()
    
    # 4. إنشاء تكوين Windsurf
    create_windsurf_config()
    
    # 5. اختبار النظام
    working = test_windsurf_continue()
    
    # 6. إنشاء سكريبت التشغيل
    create_windsurf_launcher()
    
    print("\n" + "=" * 50)
    print("📋 ملخص الحالة:")
    
    if windsurf_path:
        print(f"✅ Windsurf: {windsurf_path}")
    else:
        print("❌ Windsurf: غير موجود")
    
    if continue_ext:
        print(f"✅ Continue.dev: {continue_ext}")
    else:
        print("❌ Continue.dev: غير مثبت")
    
    if working:
        print("✅ النظام: يعمل بشكل صحيح")
    else:
        print("⚠️ النظام: يحتاج إعداد إضافي")
    
    print("\n🎯 الخطوات التالية:")
    
    if not windsurf_path:
        print("1. قم بتثبيت Windsurf IDE من: https://codeium.com/windsurf")
    
    if not continue_ext:
        print("2. ثبت Continue.dev extension يدوياً (راجع الدليل أعلاه)")
    
    if not working:
        print("3. تأكد من تشغيل خادم CodeGeeX2-6B:")
        print("   ~/start_codegeex_server.sh")
    
    print("4. أعد تشغيل Windsurf")
    print("5. اضغط Ctrl+I لفتح Continue.dev")
    
    print(f"\n💡 استخدم السكريبت: ~/start_windsurf_continue.sh")

if __name__ == "__main__":
    main()
