<?xml version="1.0" encoding="UTF-8"?>
<?mso-application progid="Excel.Sheet"?>

<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40">
	<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
		<Author/>
		<Title>agentportal_reseller_transactions_exporter</Title>
		<Description/>
		<Subject/>
	</DocumentProperties>
	<Styles>
		<Style ss:ID="HyperlinkId" ss:Name="Hyperlink">
			<Font ss:Color="#0000ff"/>
		</Style>
		<Style ss:ID="1">
			<NumberFormat ss:Format="yyyy-M-d HH:mm:ss AM/PM"/>
		</Style>
		<Style ss:ID="32">
			<Alignment ss:Horizontal="Left" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top"/>
				<Border ss:Position="Left"/>
				<Border ss:Position="Right"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="22">
			<Alignment ss:Horizontal="Left" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="21">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="25">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
			<NumberFormat ss:Format="[$-409]dd/MM/yyyy HH:mm:ss"/>
		</Style>
		<Style ss:ID="26">
			<Alignment ss:Horizontal="Right" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="29">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="30">
			<Alignment ss:Horizontal="Right" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="23">
			<Alignment ss:Horizontal="Left" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="27">
			<Alignment ss:Horizontal="Right" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="31">
			<Alignment ss:Horizontal="Right" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="20">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="24">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
			<NumberFormat ss:Format="[$-409]dd/MM/yyyy HH:mm:ss"/>
		</Style>
		<Style ss:ID="28">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
	</Styles>
	<Names>
		<NamedRange ss:Name="__bookmark_1" ss:RefersTo="agentportal_reseller_transactio!R1C1:R41C13"/>
	</Names>

<Worksheet ss:Name="agentportal_reseller_transactio">
	<ss:Table>
		<ss:Column ss:Width="50.944" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="112.512" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="96.0" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="96.768" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="83.968" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="97.536" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="166.4" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="73.6" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="89.216" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="127.488" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="104.96" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="119.296" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="110.976" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="5.888" ss:AutoFitWidth="0"/>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="21">
				<Data ss:Type="String">Row</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="21">
				<Data ss:Type="String">End Date</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">Sender ID/MSISDN</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">Receiver ID/MSISDN</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="21">
				<Data ss:Type="String">Amount</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">Transaction Type</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">Transaction Reference</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">Channel</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="21">
				<Data ss:Type="String">Result</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="21">
				<Data ss:Type="String">Sender Balance Before</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="21">
				<Data ss:Type="String">Sender Balance After</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="21">
				<Data ss:Type="String">Receiver Balance Before</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="21">
				<Data ss:Type="String">Receiver Balance After</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">1</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-25T15:48:11.835</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967735911220</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023102515481171802368269</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">5000200.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">4000200.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">39.05</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,039.05</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">2</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-24T20:29:52.713</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967736955652</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023102420295259602277564</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">38313007.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">37313007.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">12.45</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,012.45</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">3</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-20T21:12:47.933</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967739800110</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">2,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023102021124781302640640</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">6287490.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">4287490.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">15.14</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">2,000,015.14</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">4</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-19T17:17:00.546</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967736955652</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101917170042402473337</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">31686633.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">30686633.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">72.68</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,072.68</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">5</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-14T14:40:27.390</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101414402725102684640</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">1500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">69.85</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,069.85</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">6</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-14T12:49:00.581</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101412490047202673076</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">3000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">2500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">59.34</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">500,059.34</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">7</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-14T10:25:38.741</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101410253863102655675</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">3500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">3000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">265.72</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">500,265.72</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">8</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-13T18:08:34.330</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101318083420302588484</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">4000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">3500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">17.30</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">500,017.3</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">9</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-12T16:13:27.934</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101216132782402442263</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">5000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">4000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">249625.81</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,249,625.81</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">10</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-11T18:26:42.116</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101118264200102311858</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">6000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">5500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">319199.06</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">819,199.06</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">11</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-10T20:10:36.781</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101020103667601296922</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">8000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">7000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">146.26</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,146.26</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">12</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-10T15:55:06.748</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101015550663801250291</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">6000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">5000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">110.87</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,110.87</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">13</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-08T21:54:02.223</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967735632312</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100821540211802890345</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">30460000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">29460000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">80821.76</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,080,821.76</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">14</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-08T18:35:37.071</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967735632312</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100818353696802846150</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">42460000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">41460000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">44.72</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,044.72</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">15</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-06T16:18:34.960</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100616183485001705764</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">6500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">6000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">79.43</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">500,079.43</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">16</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-05T15:09:16.227</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100515091611802367661</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">11000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">10000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">80.88</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,080.88</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">17</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-05T09:40:26.121</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100509402600301533476</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">11500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">11000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">54.83</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">500,054.83</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">18</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-04T20:35:37.995</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100420353789202268963</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">12000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">11500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">68.38</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">500,068.38</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">19</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-04T16:31:00.868</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100416310073202211250</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">13500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">13000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">45.07</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">500,045.07</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">20</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-28T17:00:48.522</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">5,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023092817004840802234196</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">5000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">0.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">1008330.39</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">6,008,330.39</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">21</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-27T15:23:48.341</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023092715234823402061047</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">1000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">0.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">731608.76</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,731,608.76</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">22</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-25T15:14:04.060</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023092515140395702788868</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">1500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">0.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">646670.60</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">2,146,670.6</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">23</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-24T11:06:39.915</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023092411063979602608732</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">1500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">0.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">59.48</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,500,059.48</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">24</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-22T17:01:57.879</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023092217015776902373483</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">1000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">0.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">6.51</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,006.51</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">25</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-21T20:00:05.813</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">350,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023092120000570502291148</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">700000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">350000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">238629.82</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">588,629.82</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">26</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-21T14:57:31.193</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023092114573106902236725</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">1700000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">700000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">9.40</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,009.4</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">27</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-20T15:21:24.269</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023092015212414402094909</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">1500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">0.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">22.37</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,500,022.37</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">28</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-20T10:03:29.501</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023092010032938402050821</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">0.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">21.65</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">500,021.65</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">29</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-19T16:23:27.397</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023091916232728802957812</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">2000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">1000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">234678.34</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,234,678.34</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">30</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-18T23:37:19.765</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023091823371964601392030</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">1000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">0.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">324.03</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,324.03</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">31</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-18T01:12:39.819</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023091801123971302744328</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">2000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">1000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">0.43</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,000.43</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">32</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-16T19:56:14.551</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023091619561444402558846</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">1500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">147338.08</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,147,338.08</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">33</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-16T13:57:05.784</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023091613570565201061952</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">1500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">1000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">127.92</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">500,127.92</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">34</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-15T17:37:32.659</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023091517373252801980229</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">3500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">2500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">274978.54</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,274,978.54</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">35</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-14T15:52:40.546</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023091415524044001880813</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">1000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">0.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">229000.25</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,229,000.25</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">36</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-13T18:24:25.413</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023091318242530502192436</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">2000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">1000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">638444.62</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,638,444.62</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">37</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-12T23:01:53.706</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023091223015359802107517</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">4000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">3000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">5.04</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,005.04</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">38</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-03T00:30:02.326</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023090300300223202646955</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">1000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">0.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">996058.65</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,996,058.65</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">39</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-03T00:17:09.933</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023090300170981202646226</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">3000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">2000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">107.53</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,107.53</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">40</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-02T15:51:40.040</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967735835938</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967738470339</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023090215513992702537676</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">1320000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">320000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">72.01</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,072.01</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="32" ss:MergeAcross="13">
				<Data ss:Type="String">Feb 29, 2024, 3:56 PM</Data>
			</Cell>
		</Row>
	</ss:Table>
	<WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">
		<PageSetup>
			<PageMargins x:Bottom="0.25" x:Left="0.25" x:Right="0.25" x:Top="0.25"/>
		</PageSetup>
		<Print>
			<PaperSizeIndex>9</PaperSizeIndex>
		</Print>
	</WorksheetOptions>
</Worksheet>
</Workbook>