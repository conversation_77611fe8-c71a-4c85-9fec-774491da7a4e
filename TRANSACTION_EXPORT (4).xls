<?xml version="1.0" encoding="UTF-8"?>
<?mso-application progid="Excel.Sheet"?>

<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40">
	<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
		<Author/>
		<Title>agentportal_reseller_transactions_exporter</Title>
		<Description/>
		<Subject/>
	</DocumentProperties>
	<Styles>
		<Style ss:ID="HyperlinkId" ss:Name="Hyperlink">
			<Font ss:Color="#0000ff"/>
		</Style>
		<Style ss:ID="1">
			<NumberFormat ss:Format="yyyy-M-d HH:mm:ss AM/PM"/>
		</Style>
		<Style ss:ID="32">
			<Alignment ss:Horizontal="Left" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top"/>
				<Border ss:Position="Left"/>
				<Border ss:Position="Right"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="22">
			<Alignment ss:Horizontal="Left" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="21">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="25">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
			<NumberFormat ss:Format="[$-409]dd/MM/yyyy HH:mm:ss"/>
		</Style>
		<Style ss:ID="26">
			<Alignment ss:Horizontal="Right" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="29">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="30">
			<Alignment ss:Horizontal="Right" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="23">
			<Alignment ss:Horizontal="Left" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="27">
			<Alignment ss:Horizontal="Right" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="31">
			<Alignment ss:Horizontal="Right" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="20">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="24">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
			<NumberFormat ss:Format="[$-409]dd/MM/yyyy HH:mm:ss"/>
		</Style>
		<Style ss:ID="28">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
	</Styles>
	<Names>
		<NamedRange ss:Name="__bookmark_1" ss:RefersTo="agentportal_reseller_transactio!R1C1:R47C13"/>
	</Names>

<Worksheet ss:Name="agentportal_reseller_transactio">
	<ss:Table>
		<ss:Column ss:Width="50.944" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="112.512" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="96.0" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="96.768" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="83.968" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="97.536" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="166.4" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="73.6" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="89.216" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="127.488" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="104.96" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="119.296" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="110.976" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="5.888" ss:AutoFitWidth="0"/>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="21">
				<Data ss:Type="String">Row</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="21">
				<Data ss:Type="String">End Date</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">Sender ID/MSISDN</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">Receiver ID/MSISDN</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="21">
				<Data ss:Type="String">Amount</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">Transaction Type</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">Transaction Reference</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">Channel</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="21">
				<Data ss:Type="String">Result</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="21">
				<Data ss:Type="String">Sender Balance Before</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="21">
				<Data ss:Type="String">Sender Balance After</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="21">
				<Data ss:Type="String">Receiver Balance Before</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="21">
				<Data ss:Type="String">Receiver Balance After</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">1</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-27T17:50:12.354</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967735911220</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">700,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023102717501224802672364</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">700200.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">200.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">291449.83</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">991,449.83</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">2</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-27T17:48:03.817</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967739800110</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">300,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023102717480368101492372</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">9659760.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">9359760.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">12.31</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">300,012.31</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">3</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-26T17:03:33.326</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967735911220</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023102617033321002540206</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">5000200.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">4000200.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">48.21</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,048.21</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">4</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-24T18:06:24.494</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967735911220</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023102418062436501113849</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">6600200.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">5600200.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">674080.29</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,674,080.29</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">5</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-19T17:18:06.132</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967736955652</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">3,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101917180602002473572</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">29686633.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">26686633.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">2783053.59</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">5,783,053.59</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">6</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-15T22:34:29.415</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967739800220</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">6,852,814</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101522342930101944362</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">10000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">3147186.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">47.58</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">6,852,861.58</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">7</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-14T12:51:30.071</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101412512996502673306</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">2000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">1500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">37.04</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">500,037.04</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">8</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-11T21:59:11.015</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967734294667</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">2,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101121591090501448676</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">34045239.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">32045239.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">3302088.41</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">5,302,088.41</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">9</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-11T18:14:30.270</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101118143014702309026</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">7000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">6000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">2324133.54</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">3,324,133.54</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">10</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-07T08:29:29.679</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967734294667</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">7,950,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100708292955101772921</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">14070239.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">6120239.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">5246973.19</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">13,196,973.19</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">11</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-07T00:39:52.359</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">5,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100700395224302580689</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">5000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">0.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">360186.97</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">5,360,186.97</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">12</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-06T03:17:51.112</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100603175100402472531</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">8000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">7000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">360042.03</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,360,042.03</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">13</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-05T15:10:03.124</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100515100301901580040</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">10000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">9000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">163.74</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,163.74</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">14</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-04T16:32:01.963</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100416320186102211471</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">13000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">12500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">375385.54</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">875,385.54</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">15</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-03T16:34:40.959</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100316344085202039429</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">4500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">3500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">89.73</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,089.73</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">16</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-02T19:20:41.780</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">750,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100219204166302906756</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">750000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">0.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">557197.79</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,307,197.79</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">17</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-02T15:57:46.486</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100215574637302854836</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">2500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">1500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">33560.86</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,033,560.86</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">18</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-02T13:43:35.257</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String"/>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">131,843</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100213433520001106353</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">webadmin</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">-590324006758.33</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">-590324138601.33</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">754.83</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">132,597.83</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">19</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-29T23:27:27.664</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023092923272755202419574</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">21000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">20000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">2545573.96</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">3,545,573.96</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">20</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-29T23:26:56.507</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023092923265639702419535</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">22000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">21000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">1547515.95</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">2,547,515.95</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">21</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-29T19:22:43.808</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">2,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023092919224369202380340</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">24000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">22000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">1556802.11</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">3,556,802.11</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">22</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-29T19:22:08.106</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023092919220799502380226</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">25000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">24000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">557612.11</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,557,612.11</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">23</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-28T16:59:55.532</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">5,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023092816595540302234023</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">10000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">5000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">77963.92</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">5,077,963.92</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">24</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-27T15:20:42.890</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023092715204278202060726</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">2000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">1000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">185290.23</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,185,290.23</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">25</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-26T15:50:39.959</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023092615503984902924100</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">3000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">2000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">49644.81</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,049,644.81</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">26</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-25T15:13:39.784</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023092515133967601187455</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">3000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">1500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">1072.92</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,501,072.92</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">27</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-24T11:05:34.783</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023092411053466102608514</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">3000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">1500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">40.55</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,500,040.55</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">28</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-22T17:00:57.660</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023092217005755302373344</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">2000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">1000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">873.46</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,873.46</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">29</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-21T20:01:21.457</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">350,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023092120012135002291421</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">350000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">0.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">344.17</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">350,344.17</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">30</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-21T14:56:57.153</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023092114565705202236656</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">2700000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">1700000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">15.34</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,015.34</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">31</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-20T15:20:46.350</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023092015204623802094844</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">3000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">1500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">170.87</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,500,170.87</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">32</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-19T17:41:30.355</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023091917413023602972997</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">1000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">86829.83</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">586,829.83</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">33</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-18T23:36:54.739</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023091823365463402886284</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">2000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">1000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">467.68</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,467.68</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">34</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-18T01:13:46.001</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023091801134589602744355</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">1000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">0.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">1170.08</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,001,170.08</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">35</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-17T16:02:30.080</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023091716022995102661298</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">0.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">1162.72</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">501,162.72</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">36</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-16T19:50:54.061</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023091619505393601107942</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">2500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">1500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">1106.68</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,001,106.68</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">37</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-16T13:57:23.949</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023091613572384502504464</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">1000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">244.46</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">500,244.46</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">38</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-15T17:39:26.876</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023091517392676701980427</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">2500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">1500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">11443.24</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,011,443.24</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">39</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-14T20:28:01.063</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023091420280094401923385</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">5000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">4000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">400.32</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,400.32</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">40</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-12T23:02:45.696</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023091223024558102107564</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">3000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">2000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">306.55</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,306.55</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">41</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-06T10:52:57.676</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String"/>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">717,507.41</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023090610525762501924395</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">webadmin</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">-586640709248.31</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">-586641426755.72</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">1061042.52</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,778,549.93</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">42</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-03T00:19:53.861</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967738381100</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023090300195372202646404</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">5161333.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">3661333.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">3001273.72</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">4,501,273.72</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">43</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-03T00:19:14.895</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967738381100</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">2,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023090300191479202646369</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">7161333.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">5161333.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">1001273.72</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">3,001,273.72</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">44</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-03T00:17:25.179</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023090300172507402646252</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">2000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">1000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">1273.72</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,001,273.72</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">45</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-02T15:44:14.310</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967739087705</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023090215441420301343007</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">1053369.69</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">53369.69</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">1093.44</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,001,093.44</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">46</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-01T17:47:03.925</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">960,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023090117470379902390580</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">960000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">0.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">1368.43</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">961,368.43</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="32" ss:MergeAcross="13">
				<Data ss:Type="String">Feb 29, 2024, 3:39 PM</Data>
			</Cell>
		</Row>
	</ss:Table>
	<WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">
		<PageSetup>
			<PageMargins x:Bottom="0.25" x:Left="0.25" x:Right="0.25" x:Top="0.25"/>
		</PageSetup>
		<Print>
			<PaperSizeIndex>9</PaperSizeIndex>
		</Print>
	</WorksheetOptions>
</Worksheet>
</Workbook>