<?xml version="1.0" encoding="UTF-8"?>
<?mso-application progid="Excel.Sheet"?>

<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40">
	<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
		<Author/>
		<Title>agentportal_reseller_transactions_exporter</Title>
		<Description/>
		<Subject/>
	</DocumentProperties>
	<Styles>
		<Style ss:ID="HyperlinkId" ss:Name="Hyperlink">
			<Font ss:Color="#0000ff"/>
		</Style>
		<Style ss:ID="1">
			<NumberFormat ss:Format="yyyy-M-d HH:mm:ss AM/PM"/>
		</Style>
		<Style ss:ID="32">
			<Alignment ss:Horizontal="Left" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top"/>
				<Border ss:Position="Left"/>
				<Border ss:Position="Right"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="22">
			<Alignment ss:Horizontal="Left" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="21">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="25">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
			<NumberFormat ss:Format="[$-409]dd/MM/yyyy HH:mm:ss"/>
		</Style>
		<Style ss:ID="26">
			<Alignment ss:Horizontal="Right" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="29">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="30">
			<Alignment ss:Horizontal="Right" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="23">
			<Alignment ss:Horizontal="Left" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="27">
			<Alignment ss:Horizontal="Right" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="31">
			<Alignment ss:Horizontal="Right" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="20">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="24">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
			<NumberFormat ss:Format="[$-409]dd/MM/yyyy HH:mm:ss"/>
		</Style>
		<Style ss:ID="28">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
	</Styles>
	<Names>
		<NamedRange ss:Name="__bookmark_1" ss:RefersTo="agentportal_reseller_transactio!R1C1:R12C13"/>
	</Names>

<Worksheet ss:Name="agentportal_reseller_transactio">
	<ss:Table>
		<ss:Column ss:Width="50.944" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="112.512" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="96.0" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="96.768" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="83.968" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="97.536" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="166.4" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="73.6" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="89.216" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="127.488" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="104.96" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="119.296" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="110.976" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="5.888" ss:AutoFitWidth="0"/>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="21">
				<Data ss:Type="String">Row</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="21">
				<Data ss:Type="String">End Date</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">Sender ID/MSISDN</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">Receiver ID/MSISDN</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="21">
				<Data ss:Type="String">Amount</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">Transaction Type</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">Transaction Reference</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">Channel</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="21">
				<Data ss:Type="String">Result</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="21">
				<Data ss:Type="String">Sender Balance Before</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="21">
				<Data ss:Type="String">Sender Balance After</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="21">
				<Data ss:Type="String">Receiver Balance Before</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="21">
				<Data ss:Type="String">Receiver Balance After</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">1</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-25T15:48:45.664</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967735911220</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735877647</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023102515484554701222694</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">4000200.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">3000200.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">742244.68</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,742,244.68</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">2</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-24T20:30:23.600</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967736955652</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735877647</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023102420302350302277677</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">37313007.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">36313007.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">81.46</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,081.46</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">3</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-20T21:11:31.290</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967739800110</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735877647</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">2,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023102021113115202640417</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">8287490.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">6287490.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">20.28</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">2,000,020.28</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">4</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-19T17:17:32.982</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967736955652</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735877647</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101917173287102473450</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">30686633.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">29686633.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">270438.01</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,270,438.01</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">5</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-08T21:53:31.465</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967735632312</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735877647</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100821533133201038659</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">31460000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">30460000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">1000008.05</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">2,000,008.05</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">6</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-08T18:35:06.970</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967735632312</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735877647</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100818350686502846015</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">43460000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">42460000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">8.05</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,008.05</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">7</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-05T15:13:16.416</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735877647</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100515131628801580474</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">8500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">8000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">415065.09</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">915,065.09</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">8</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-04T16:33:08.853</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735877647</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100416330874202211716</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">12500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">12000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">80.09</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">500,080.09</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">9</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-02T19:20:08.264</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735877647</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">750,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100219200814802906639</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">1500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">750000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">8476.89</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">758,476.89</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">10</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-02T15:57:22.526</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735877647</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100215572241302854750</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">2000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">1500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">8700.65</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">508,700.65</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">11</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-09-14T20:28:44.665</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735877647</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023091420284456402342973</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">4000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">3500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">94.14</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">500,094.14</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="32" ss:MergeAcross="13">
				<Data ss:Type="String">Feb 29, 2024, 4:00 PM</Data>
			</Cell>
		</Row>
	</ss:Table>
	<WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">
		<PageSetup>
			<PageMargins x:Bottom="0.25" x:Left="0.25" x:Right="0.25" x:Top="0.25"/>
		</PageSetup>
		<Print>
			<PaperSizeIndex>9</PaperSizeIndex>
		</Print>
	</WorksheetOptions>
</Worksheet>
</Workbook>