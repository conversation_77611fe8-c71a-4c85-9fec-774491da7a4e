<?xml version="1.0" encoding="UTF-8"?>
<?mso-application progid="Excel.Sheet"?>

<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40">
	<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
		<Author/>
		<Title>agentportal_reseller_transactions_exporter</Title>
		<Description/>
		<Subject/>
	</DocumentProperties>
	<Styles>
		<Style ss:ID="HyperlinkId" ss:Name="Hyperlink">
			<Font ss:Color="#0000ff"/>
		</Style>
		<Style ss:ID="1">
			<NumberFormat ss:Format="yyyy-M-d HH:mm:ss AM/PM"/>
		</Style>
		<Style ss:ID="32">
			<Alignment ss:Horizontal="Left" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top"/>
				<Border ss:Position="Left"/>
				<Border ss:Position="Right"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="22">
			<Alignment ss:Horizontal="Left" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="21">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="25">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
			<NumberFormat ss:Format="[$-409]dd/MM/yyyy HH:mm:ss"/>
		</Style>
		<Style ss:ID="26">
			<Alignment ss:Horizontal="Right" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="29">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="30">
			<Alignment ss:Horizontal="Right" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="23">
			<Alignment ss:Horizontal="Left" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="27">
			<Alignment ss:Horizontal="Right" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="31">
			<Alignment ss:Horizontal="Right" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="20">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="24">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
			<NumberFormat ss:Format="[$-409]dd/MM/yyyy HH:mm:ss"/>
		</Style>
		<Style ss:ID="28">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
	</Styles>
	<Names>
		<NamedRange ss:Name="__bookmark_1" ss:RefersTo="agentportal_reseller_transactio!R1C1:R6C13"/>
	</Names>

<Worksheet ss:Name="agentportal_reseller_transactio">
	<ss:Table>
		<ss:Column ss:Width="50.944" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="112.512" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="96.0" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="96.768" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="83.968" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="97.536" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="166.4" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="73.6" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="89.216" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="127.488" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="104.96" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="119.296" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="110.976" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="5.888" ss:AutoFitWidth="0"/>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="21">
				<Data ss:Type="String">Row</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="21">
				<Data ss:Type="String">End Date</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">Sender ID/MSISDN</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">Receiver ID/MSISDN</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="21">
				<Data ss:Type="String">Amount</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">Transaction Type</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">Transaction Reference</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">Channel</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="21">
				<Data ss:Type="String">Result</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="21">
				<Data ss:Type="String">Sender Balance Before</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="21">
				<Data ss:Type="String">Sender Balance After</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="21">
				<Data ss:Type="String">Receiver Balance Before</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="21">
				<Data ss:Type="String">Receiver Balance After</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">1</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-12-21T22:08:15.472</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967738981100</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">6,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023122122081532701497947</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">79488054.61</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">73488054.61</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">1612970.52</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">7,612,970.52</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">2</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-12-17T17:33:37.410</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967738981100</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">6,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023121717333729901946499</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">143488054.61</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">137488054.61</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">1228.37</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">6,001,228.37</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">3</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-11-26T15:51:07.015</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967739888611</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">5,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023112615510691002300380</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">1067953450.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">1062953450.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">355.32</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">5,000,355.32</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">4</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-11-16T14:39:29.039</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967739888611</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">5,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023111614392893401294005</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">546932315.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">541932315.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">43.21</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">5,000,043.21</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">5</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-11-05T10:58:04.334</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967739888611</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">5,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023110510580420501742417</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">249369005.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">244369005.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">76.43</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">5,000,076.43</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="32" ss:MergeAcross="13">
				<Data ss:Type="String">Feb 29, 2024, 3:37 PM</Data>
			</Cell>
		</Row>
	</ss:Table>
	<WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">
		<PageSetup>
			<PageMargins x:Bottom="0.25" x:Left="0.25" x:Right="0.25" x:Top="0.25"/>
		</PageSetup>
		<Print>
			<PaperSizeIndex>9</PaperSizeIndex>
		</Print>
	</WorksheetOptions>
</Worksheet>
</Workbook>