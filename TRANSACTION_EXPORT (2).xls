<?xml version="1.0"?>
<?mso-application progid="Excel.Sheet"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 <DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
  <Title>agentportal_reseller_transactions_exporter</Title>
  <LastAuthor>moh hash</LastAuthor>
  <LastSaved>2024-02-18T07:57:00Z</LastSaved>
  <Version>16.00</Version>
 </DocumentProperties>
 <OfficeDocumentSettings xmlns="urn:schemas-microsoft-com:office:office">
  <AllowPNG/>
 </OfficeDocumentSettings>
 <ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">
  <WindowHeight>8100</WindowHeight>
  <WindowWidth>21408</WindowWidth>
  <WindowTopX>32767</WindowTopX>
  <WindowTopY>32767</WindowTopY>
  <ProtectStructure>False</ProtectStructure>
  <ProtectWindows>False</ProtectWindows>
 </ExcelWorkbook>
 <Styles>
  <Style ss:ID="Default" ss:Name="Normal">
   <Alignment ss:Vertical="Bottom"/>
   <Borders/>
   <Font ss:FontName="Arial" x:Family="Swiss" ss:Size="11" ss:Color="#000000"/>
   <Interior/>
   <NumberFormat/>
   <Protection/>
  </Style>
  <Style ss:ID="s63">
   <Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#000000"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#000000"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#000000"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#000000"/>
   </Borders>
   <Font ss:FontName="sans-serif" ss:Color="#000000"/>
   <Interior/>
   <NumberFormat/>
   <Protection/>
  </Style>
  <Style ss:ID="s64">
   <Alignment ss:Horizontal="Left" ss:Vertical="Top" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#000000"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#000000"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#000000"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#000000"/>
   </Borders>
   <Font ss:FontName="sans-serif" ss:Color="#000000"/>
   <Interior/>
   <NumberFormat/>
   <Protection/>
  </Style>
  <Style ss:ID="s65">
   <Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#000000"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#000000"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#000000"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#000000"/>
   </Borders>
   <Font ss:FontName="sans-serif" ss:Color="#000000"/>
   <Interior/>
   <NumberFormat ss:Format="[$-409]dd/mm/yyyy\ hh:mm:ss"/>
   <Protection/>
  </Style>
  <Style ss:ID="s66">
   <Alignment ss:Horizontal="Right" ss:Vertical="Top" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#000000"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#000000"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#000000"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#000000"/>
   </Borders>
   <Font ss:FontName="sans-serif" ss:Color="#000000"/>
   <Interior/>
   <NumberFormat/>
   <Protection/>
  </Style>
 </Styles>
 <Names>
  <NamedRange ss:Name="__bookmark_1"
   ss:RefersTo="=agentportal_reseller_transactio!R1C1:R3C13"/>
 </Names>
 <Worksheet ss:Name="agentportal_reseller_transactio">
  <Table ss:ExpandedColumnCount="14" ss:ExpandedRowCount="3" x:FullColumns="1"
   x:FullRows="1" ss:DefaultColumnWidth="52.8" ss:DefaultRowHeight="13.8">
   <Column ss:AutoFitWidth="0" ss:Width="51"/>
   <Column ss:AutoFitWidth="0" ss:Width="112.8"/>
   <Column ss:AutoFitWidth="0" ss:Width="96"/>
   <Column ss:AutoFitWidth="0" ss:Width="96.6"/>
   <Column ss:AutoFitWidth="0" ss:Width="84"/>
   <Column ss:AutoFitWidth="0" ss:Width="97.800000000000011"/>
   <Column ss:AutoFitWidth="0" ss:Width="166.2"/>
   <Column ss:AutoFitWidth="0" ss:Width="73.8"/>
   <Column ss:AutoFitWidth="0" ss:Width="89.4"/>
   <Column ss:AutoFitWidth="0" ss:Width="127.19999999999999"/>
   <Column ss:AutoFitWidth="0" ss:Width="105"/>
   <Column ss:AutoFitWidth="0" ss:Width="119.4"/>
   <Column ss:AutoFitWidth="0" ss:Width="111"/>
   <Column ss:AutoFitWidth="0" ss:Width="6"/>
   <Row ss:Height="26.4">
    <Cell ss:StyleID="s63"><Data ss:Type="String">Row</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">End Date</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">Sender ID/MSISDN</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">Receiver ID/MSISDN</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">Amount</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">Transaction Type</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">Transaction Reference</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">Channel</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">Result</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">Sender Balance Before</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">Sender Balance After</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">Receiver Balance Before</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">Receiver Balance After</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
   </Row>
   <Row ss:Height="26.4">
    <Cell ss:StyleID="s64"><Data ss:Type="Number">1</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="DateTime">2024-01-07T16:52:04.299</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">967739888611</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">967730663111</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="String">5,000,000</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">CREDIT_TRANSFER</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2024010716520419801617186</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">USSD</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="Number">0</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="Number">1307922624</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="Number">1302922624</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="Number">81697.539999999994</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="String">5,081,697.54</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
   </Row>
   <Row ss:Height="26.4">
    <Cell ss:StyleID="s64"><Data ss:Type="Number">2</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="DateTime">2024-01-02T20:13:02.563</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">967739888611</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">967730663111</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="String">5,000,000</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">CREDIT_TRANSFER</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2024010220130246401013146</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">USSD</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="Number">0</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="Number">1668315843</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="Number">1663315843</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="Number">18.190000000000001</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="String">5,000,018.19</Data><NamedCell
      ss:Name="__bookmark_1"/></Cell>
   </Row>
  </Table>
  <WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">
   <PageSetup>
    <PageMargins x:Bottom="0.25" x:Left="0.25" x:Right="0.25" x:Top="0.25"/>
   </PageSetup>
   <Selected/>
   <Panes>
    <Pane>
     <Number>3</Number>
     <ActiveRow>3</ActiveRow>
     <RangeSelection>R4</RangeSelection>
    </Pane>
   </Panes>
   <ProtectObjects>False</ProtectObjects>
   <ProtectScenarios>False</ProtectScenarios>
  </WorksheetOptions>
 </Worksheet>
</Workbook>
