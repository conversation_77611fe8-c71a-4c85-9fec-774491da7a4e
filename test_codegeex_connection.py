#!/usr/bin/env python3
"""
سكريبت لاختبار الاتصال مع نموذج CodeGeeX2-6B المحلي
"""

import requests
import json
import sys

def test_connection():
    """اختبار الاتصال مع الخادم المحلي"""
    
    # إعدادات الاتصال
    base_url = "http://localhost:8001/v1"
    api_key = "sk-local-codegeex-key-12345"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    print("🔍 اختبار الاتصال مع خادم CodeGeeX2-6B المحلي...")
    
    # 1. اختبار قائمة النماذج
    try:
        print("\n1️⃣ اختبار قائمة النماذج...")
        response = requests.get(f"{base_url}/models", headers=headers, timeout=10)
        
        if response.status_code == 200:
            models = response.json()
            print("✅ تم الحصول على قائمة النماذج بنجاح:")
            for model in models.get("data", []):
                print(f"   - {model.get('id', 'غير محدد')}")
        else:
            print(f"❌ فشل في الحصول على قائمة النماذج: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False
    
    # 2. اختبار إكمال النص
    try:
        print("\n2️⃣ اختبار إكمال النص...")
        
        test_data = {
            "model": "THUDM/codegeex2-6b",
            "messages": [
                {
                    "role": "user", 
                    "content": "اكتب دالة Python بسيطة لحساب مضروب عدد"
                }
            ],
            "max_tokens": 200,
            "temperature": 0.2,
            "top_p": 0.95
        }
        
        response = requests.post(
            f"{base_url}/chat/completions", 
            headers=headers, 
            json=test_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result["choices"][0]["message"]["content"]
            print("✅ تم إنشاء النص بنجاح:")
            print(f"📝 الرد: {content[:200]}...")
            
            # إحصائيات الاستخدام
            usage = result.get("usage", {})
            print(f"📊 الإحصائيات:")
            print(f"   - رموز الطلب: {usage.get('prompt_tokens', 0)}")
            print(f"   - رموز الإكمال: {usage.get('completion_tokens', 0)}")
            print(f"   - إجمالي الرموز: {usage.get('total_tokens', 0)}")
            
        else:
            print(f"❌ فشل في إكمال النص: {response.status_code}")
            print(f"📄 الرد: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False
    
    # 3. اختبار إكمال الكود
    try:
        print("\n3️⃣ اختبار إكمال الكود...")
        
        code_completion_data = {
            "model": "THUDM/codegeex2-6b",
            "messages": [
                {
                    "role": "user", 
                    "content": "def fibonacci(n):\n    # أكمل هذه الدالة لحساب متتالية فيبوناتشي"
                }
            ],
            "max_tokens": 150,
            "temperature": 0.1,
            "stop": ["\n\n", "def ", "class "]
        }
        
        response = requests.post(
            f"{base_url}/chat/completions", 
            headers=headers, 
            json=code_completion_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result["choices"][0]["message"]["content"]
            print("✅ تم إكمال الكود بنجاح:")
            print(f"💻 الكود المكتمل:\n{content}")
        else:
            print(f"❌ فشل في إكمال الكود: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False
    
    print("\n🎉 جميع الاختبارات نجحت! النموذج يعمل بشكل صحيح.")
    return True

def check_continue_config():
    """فحص تكوين Continue.dev"""
    
    print("\n🔧 فحص تكوين Continue.dev...")
    
    import os
    config_path = os.path.expanduser("~/.continue/config.json")
    
    if not os.path.exists(config_path):
        print("❌ ملف التكوين غير موجود")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # فحص النماذج
        models = config.get("models", [])
        if not models:
            print("❌ لا توجد نماذج مكونة")
            return False
        
        main_model = models[0]
        print(f"✅ النموذج الرئيسي: {main_model.get('title', 'غير محدد')}")
        print(f"   - المزود: {main_model.get('provider', 'غير محدد')}")
        print(f"   - النموذج: {main_model.get('model', 'غير محدد')}")
        print(f"   - عنوان API: {main_model.get('apiBase', 'غير محدد')}")
        
        # فحص نموذج الإكمال التلقائي
        autocomplete = config.get("tabAutocompleteModel", {})
        if autocomplete:
            print(f"✅ نموذج الإكمال التلقائي: {autocomplete.get('title', 'غير محدد')}")
        
        # فحص الأوامر المخصصة
        custom_commands = config.get("customCommands", [])
        print(f"✅ الأوامر المخصصة: {len(custom_commands)} أمر")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف التكوين: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار نموذج CodeGeeX2-6B مع Continue.dev")
    print("=" * 60)
    
    # فحص التكوين
    config_ok = check_continue_config()
    
    # اختبار الاتصال
    connection_ok = test_connection()
    
    print("\n" + "=" * 60)
    if config_ok and connection_ok:
        print("🎉 النظام جاهز للاستخدام!")
        print("💡 يمكنك الآن استخدام Continue.dev مع CodeGeeX2-6B")
        sys.exit(0)
    else:
        print("❌ هناك مشاكل تحتاج إلى إصلاح")
        sys.exit(1)
