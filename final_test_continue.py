#!/usr/bin/env python3
"""
اختبار نهائي شامل لنظام Continue.dev مع CodeGeeX2-6B
"""

import requests
import json
import time
import os
from pathlib import Path

def test_api_endpoints():
    """اختبار جميع نقاط API"""
    
    base_url = "http://localhost:8001/v1"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer sk-local-codegeex-key-12345"
    }
    
    tests = []
    
    # 1. اختبار قائمة النماذج
    try:
        response = requests.get(f"{base_url}/models", headers=headers, timeout=5)
        tests.append({
            "name": "قائمة النماذج",
            "status": "✅ نجح" if response.status_code == 200 else f"❌ فشل ({response.status_code})",
            "details": response.json() if response.status_code == 200 else response.text[:100]
        })
    except Exception as e:
        tests.append({
            "name": "قائمة النماذج",
            "status": f"❌ خطأ: {str(e)[:50]}",
            "details": ""
        })
    
    # 2. اختبار إكمال النص العربي
    try:
        data = {
            "model": "THUDM/codegeex2-6b",
            "messages": [{"role": "user", "content": "اكتب دالة Python لحساب مضروب عدد"}],
            "max_tokens": 200,
            "temperature": 0.2
        }
        response = requests.post(f"{base_url}/chat/completions", headers=headers, json=data, timeout=15)
        
        if response.status_code == 200:
            result = response.json()
            content = result["choices"][0]["message"]["content"]
            tests.append({
                "name": "إكمال النص العربي",
                "status": "✅ نجح",
                "details": f"طول الرد: {len(content)} حرف"
            })
        else:
            tests.append({
                "name": "إكمال النص العربي",
                "status": f"❌ فشل ({response.status_code})",
                "details": response.text[:100]
            })
    except Exception as e:
        tests.append({
            "name": "إكمال النص العربي",
            "status": f"❌ خطأ: {str(e)[:50]}",
            "details": ""
        })
    
    # 3. اختبار إكمال الكود
    try:
        data = {
            "model": "THUDM/codegeex2-6b",
            "messages": [{"role": "user", "content": "def fibonacci(n):\n    # أكمل هذه الدالة"}],
            "max_tokens": 150,
            "temperature": 0.1,
            "stop": ["\n\n", "def "]
        }
        response = requests.post(f"{base_url}/chat/completions", headers=headers, json=data, timeout=15)
        
        if response.status_code == 200:
            result = response.json()
            content = result["choices"][0]["message"]["content"]
            tests.append({
                "name": "إكمال الكود",
                "status": "✅ نجح",
                "details": f"طول الكود: {len(content)} حرف"
            })
        else:
            tests.append({
                "name": "إكمال الكود",
                "status": f"❌ فشل ({response.status_code})",
                "details": response.text[:100]
            })
    except Exception as e:
        tests.append({
            "name": "إكمال الكود",
            "status": f"❌ خطأ: {str(e)[:50]}",
            "details": ""
        })
    
    return tests

def test_continue_config():
    """اختبار تكوين Continue.dev"""
    
    config_path = Path.home() / ".continue" / "config.json"
    tests = []
    
    # 1. وجود ملف التكوين
    if config_path.exists():
        tests.append({
            "name": "وجود ملف التكوين",
            "status": "✅ موجود",
            "details": str(config_path)
        })
    else:
        tests.append({
            "name": "وجود ملف التكوين",
            "status": "❌ غير موجود",
            "details": str(config_path)
        })
        return tests
    
    # 2. صحة تنسيق JSON
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        tests.append({
            "name": "صحة تنسيق JSON",
            "status": "✅ صحيح",
            "details": f"حجم الملف: {config_path.stat().st_size} بايت"
        })
    except Exception as e:
        tests.append({
            "name": "صحة تنسيق JSON",
            "status": f"❌ خطأ: {str(e)[:50]}",
            "details": ""
        })
        return tests
    
    # 3. وجود النماذج
    models = config.get("models", [])
    if models and len(models) > 0:
        main_model = models[0]
        tests.append({
            "name": "تكوين النموذج الرئيسي",
            "status": "✅ مكون",
            "details": f"النموذج: {main_model.get('model', 'غير محدد')}"
        })
    else:
        tests.append({
            "name": "تكوين النموذج الرئيسي",
            "status": "❌ غير مكون",
            "details": "لا توجد نماذج"
        })
    
    # 4. نموذج الإكمال التلقائي
    autocomplete = config.get("tabAutocompleteModel", {})
    if autocomplete:
        tests.append({
            "name": "نموذج الإكمال التلقائي",
            "status": "✅ مكون",
            "details": f"النموذج: {autocomplete.get('model', 'غير محدد')}"
        })
    else:
        tests.append({
            "name": "نموذج الإكمال التلقائي",
            "status": "❌ غير مكون",
            "details": ""
        })
    
    # 5. الأوامر المخصصة
    custom_commands = config.get("customCommands", [])
    tests.append({
        "name": "الأوامر المخصصة",
        "status": f"✅ {len(custom_commands)} أمر" if custom_commands else "⚠️ لا توجد أوامر",
        "details": ", ".join([cmd.get('name', 'بدون اسم') for cmd in custom_commands[:3]])
    })
    
    return tests

def test_performance():
    """اختبار الأداء"""
    
    tests = []
    
    # 1. زمن الاستجابة
    start_time = time.time()
    try:
        response = requests.get("http://localhost:8001/v1/models", timeout=5)
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            if response_time < 1:
                status = "✅ سريع"
            elif response_time < 3:
                status = "⚠️ متوسط"
            else:
                status = "❌ بطيء"
            
            tests.append({
                "name": "زمن الاستجابة",
                "status": status,
                "details": f"{response_time:.2f} ثانية"
            })
        else:
            tests.append({
                "name": "زمن الاستجابة",
                "status": "❌ فشل الاتصال",
                "details": f"كود الخطأ: {response.status_code}"
            })
    except Exception as e:
        tests.append({
            "name": "زمن الاستجابة",
            "status": f"❌ خطأ: {str(e)[:30]}",
            "details": ""
        })
    
    # 2. اختبار الذاكرة (تقريبي)
    try:
        import psutil
        memory = psutil.virtual_memory()
        tests.append({
            "name": "استخدام الذاكرة",
            "status": f"📊 {memory.percent}%",
            "details": f"متاح: {memory.available // (1024**3)} GB"
        })
    except ImportError:
        tests.append({
            "name": "استخدام الذاكرة",
            "status": "⚠️ غير متاح",
            "details": "psutil غير مثبت"
        })
    
    return tests

def generate_report(api_tests, config_tests, performance_tests):
    """إنشاء تقرير شامل"""
    
    print("🔍 تقرير الاختبار النهائي لنظام Continue.dev مع CodeGeeX2-6B")
    print("=" * 80)
    
    # اختبارات API
    print("\n🌐 اختبارات API:")
    for test in api_tests:
        print(f"   {test['status']} {test['name']}")
        if test['details']:
            print(f"      📝 {test['details']}")
    
    # اختبارات التكوين
    print("\n⚙️ اختبارات التكوين:")
    for test in config_tests:
        print(f"   {test['status']} {test['name']}")
        if test['details']:
            print(f"      📝 {test['details']}")
    
    # اختبارات الأداء
    print("\n⚡ اختبارات الأداء:")
    for test in performance_tests:
        print(f"   {test['status']} {test['name']}")
        if test['details']:
            print(f"      📝 {test['details']}")
    
    # تقييم عام
    all_tests = api_tests + config_tests + performance_tests
    passed = sum(1 for test in all_tests if "✅" in test['status'])
    total = len(all_tests)
    
    print(f"\n📊 النتيجة الإجمالية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 ممتاز! النظام يعمل بشكل مثالي")
        return True
    elif passed >= total * 0.8:
        print("✅ جيد! النظام يعمل بشكل جيد مع بعض التحسينات المطلوبة")
        return True
    else:
        print("⚠️ يحتاج إصلاح! هناك مشاكل تحتاج إلى حل")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء الاختبار النهائي الشامل...")
    print("⏳ هذا قد يستغرق بضع ثوانٍ...\n")
    
    # تشغيل الاختبارات
    api_tests = test_api_endpoints()
    config_tests = test_continue_config()
    performance_tests = test_performance()
    
    # إنشاء التقرير
    success = generate_report(api_tests, config_tests, performance_tests)
    
    print("\n" + "=" * 80)
    
    if success:
        print("✅ النظام جاهز للاستخدام!")
        print("💡 يمكنك الآن فتح VS Code واستخدام Continue.dev")
        print("📖 راجع الدليل: دليل_استخدام_Continue_CodeGeeX.md")
    else:
        print("❌ يرجى إصلاح المشاكل المذكورة أعلاه")
        print("🔧 جرب تشغيل: python3 fix_continue_codegeex.py")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
