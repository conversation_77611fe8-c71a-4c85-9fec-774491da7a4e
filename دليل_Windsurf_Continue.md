# دليل استخدام Continue.dev في Windsurf IDE

## 🎉 تم الإعداد بنجاح!

Continue.dev الآن مثبت ومكون للعمل مع CodeGeeX2-6B في Windsurf IDE.

## 🚀 كيفية فتح Continue.dev في Windsurf

### الطريقة الأولى: اختصار لوحة المفاتيح
```
اضغط Ctrl+I
```

### الطريقة الثانية: Command Palette
1. اضغط `Ctrl+Shift+P` (أو `Cmd+Shift+P` على Mac)
2. اكتب "Continue"
3. اختر "Continue: Open Continue"

### الطريقة الثالثة: من القائمة
1. اذهب إلى `View` في القائمة العلوية
2. ابحث عن "Continue" أو "AI Assistant"

## 🔍 إذا لم تظهر Continue.dev

### 1. تحقق من تثبيت Extension
```bash
# تحقق من وجود Continue.dev
ls ~/.vscode/extensions/ | grep continue
```

### 2. أعد تشغيل Windsurf
- أغلق Windsurf تماماً
- افتحه مرة أخرى
- جرب `Ctrl+I`

### 3. تفعيل Extension يدوياً
1. اضغط `Ctrl+Shift+X` لفتح Extensions
2. ابحث عن "Continue"
3. تأكد أنه مفعل (Enable)

### 4. تثبيت Continue.dev يدوياً
إذا لم يكن مثبتاً:

1. **من Marketplace:**
   - افتح Extensions (`Ctrl+Shift+X`)
   - ابحث عن "Continue"
   - اختر "Continue - Codestral, Claude, and more"
   - اضغط Install

2. **تثبيت يدوي:**
   ```bash
   # في Terminal
   windsurf --install-extension Continue.continue
   ```

## 🎯 كيفية الاستخدام

### 1. فتح Continue.dev
```
اضغط Ctrl+I
```

### 2. استخدام الأوامر المخصصة
في نافذة Continue.dev، اكتب:

- `/شرح الكود` - لشرح الكود المحدد
- `/إصلاح الأخطاء` - لإيجاد وإصلاح الأخطاء
- `/تحسين الكود` - لتحسين الكود
- `/إنشاء اختبارات` - لإنشاء اختبارات الوحدة
- `/توثيق الكود` - لإضافة التوثيق
- `/تحويل إلى Python` - لتحويل الكود إلى Python

### 3. الإكمال التلقائي
- يعمل تلقائياً أثناء الكتابة
- اضغط `Tab` لقبول الاقتراح
- اضغط `Esc` لرفض الاقتراح

### 4. طرح الأسئلة
اكتب أي سؤال برمجي مثل:
- "كيف أنشئ دالة لحساب المضروب؟"
- "اشرح لي هذا الكود"
- "ما هي أفضل طريقة لقراءة ملف CSV؟"

## 🔧 استكشاف الأخطاء

### المشكلة: Continue.dev لا يظهر
**الحل:**
```bash
# 1. تحقق من التثبيت
python3 install_continue_windsurf.py

# 2. أعد تشغيل Windsurf
pkill windsurf
windsurf

# 3. تحقق من الخادم
curl http://localhost:8001/v1/models
```

### المشكلة: "No models configured"
**الحل:**
```bash
# تحقق من التكوين
cat ~/.continue/config.json

# أعد إنشاء التكوين
python3 fix_continue_codegeex.py
```

### المشكلة: الخادم لا يعمل
**الحل:**
```bash
# تشغيل الخادم
~/start_codegeex_server.sh

# أو تحقق من حالة الخادم
python3 test_codegeex_connection.py
```

## 📱 واجهة Continue.dev

عند فتح Continue.dev ستجد:

### 1. نافذة الدردشة
- مكان لكتابة الأسئلة والأوامر
- عرض الردود من النموذج

### 2. أزرار الإجراءات
- **Apply** - تطبيق التغييرات المقترحة
- **Copy** - نسخ الكود
- **Insert** - إدراج الكود في المحرر

### 3. شريط الأوامر
- `/` لعرض الأوامر المتاحة
- `@` لاختيار ملفات محددة

## 🎨 تخصيص Continue.dev

### تغيير الإعدادات
1. اضغط على أيقونة الإعدادات في Continue.dev
2. أو عدل الملف: `~/.continue/config.json`

### إضافة أوامر جديدة
```json
{
  "customCommands": [
    {
      "name": "اسم الأمر الجديد",
      "prompt": "وصف المهمة:\n\n{{{ input }}}\n\nالنتيجة:"
    }
  ]
}
```

## 🚀 نصائح للاستخدام الأمثل

### 1. حدد الكود قبل السؤال
- حدد الكود الذي تريد العمل عليه
- ثم استخدم الأوامر المخصصة

### 2. استخدم أوصاف واضحة
- بدلاً من "اصلح هذا"
- اكتب "اصلح خطأ الفهرسة في هذه الحلقة"

### 3. استفد من السياق
- Continue.dev يفهم ملفات المشروع
- يمكنه الإشارة إلى ملفات أخرى

### 4. جرب درجات حرارة مختلفة
- للكود الدقيق: temperature = 0.1
- للأفكار الإبداعية: temperature = 0.7

## 📊 مراقبة الأداء

### تحقق من حالة النظام
```bash
python3 final_test_continue.py
```

### مراقبة استخدام الموارد
```bash
# استخدام الذاكرة
free -h

# استخدام المعالج
htop
```

## 🔄 الصيانة

### تحديث التكوين
```bash
python3 fix_continue_codegeex.py
```

### إعادة تشغيل الخادم
```bash
~/start_codegeex_server.sh
```

### تشغيل Windsurf مع Continue.dev
```bash
~/start_windsurf_continue.sh
```

## 🎉 الخلاصة

Continue.dev الآن يعمل في Windsurf مع:

- ✅ **نموذج CodeGeeX2-6B محلي**
- ✅ **أوامر مخصصة باللغة العربية**
- ✅ **إكمال تلقائي ذكي**
- ✅ **أمان كامل (محلي 100%)**

**🚀 ابدأ الآن: اضغط `Ctrl+I` في Windsurf!**

---

## 📞 المساعدة

إذا واجهت أي مشاكل:

1. **راجع هذا الدليل**
2. **شغل سكريبت التشخيص:**
   ```bash
   python3 install_continue_windsurf.py
   ```
3. **تحقق من السجلات في Windsurf:**
   - `Help > Toggle Developer Tools > Console`
