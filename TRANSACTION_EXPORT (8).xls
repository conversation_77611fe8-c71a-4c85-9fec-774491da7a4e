<?xml version="1.0" encoding="UTF-8"?>
<?mso-application progid="Excel.Sheet"?>

<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40">
	<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
		<Author/>
		<Title>agentportal_reseller_transactions_exporter</Title>
		<Description/>
		<Subject/>
	</DocumentProperties>
	<Styles>
		<Style ss:ID="HyperlinkId" ss:Name="Hyperlink">
			<Font ss:Color="#0000ff"/>
		</Style>
		<Style ss:ID="1">
			<NumberFormat ss:Format="yyyy-M-d HH:mm:ss AM/PM"/>
		</Style>
		<Style ss:ID="32">
			<Alignment ss:Horizontal="Left" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top"/>
				<Border ss:Position="Left"/>
				<Border ss:Position="Right"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="22">
			<Alignment ss:Horizontal="Left" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="21">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="25">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
			<NumberFormat ss:Format="[$-409]dd/MM/yyyy HH:mm:ss"/>
		</Style>
		<Style ss:ID="26">
			<Alignment ss:Horizontal="Right" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="29">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="30">
			<Alignment ss:Horizontal="Right" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="23">
			<Alignment ss:Horizontal="Left" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="27">
			<Alignment ss:Horizontal="Right" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="31">
			<Alignment ss:Horizontal="Right" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="20">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
		<Style ss:ID="24">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
			<NumberFormat ss:Format="[$-409]dd/MM/yyyy HH:mm:ss"/>
		</Style>
		<Style ss:ID="28">
			<Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1"/>
			<Borders>
				<Border ss:Position="Bottom"/>
				<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#000000"/>
				<Border ss:Position="DiagonalLeft"/>
			</Borders>
			<Font ss:FontName="sans-serif" ss:Size="10.0" ss:Color="#000000"/>
		</Style>
	</Styles>
	<Names>
		<NamedRange ss:Name="__bookmark_1" ss:RefersTo="agentportal_reseller_transactio!R1C1:R23C13"/>
	</Names>

<Worksheet ss:Name="agentportal_reseller_transactio">
	<ss:Table>
		<ss:Column ss:Width="50.944" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="112.512" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="96.0" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="96.768" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="83.968" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="97.536" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="166.4" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="73.6" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="89.216" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="127.488" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="104.96" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="119.296" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="110.976" ss:AutoFitWidth="0"/>
		<ss:Column ss:Width="5.888" ss:AutoFitWidth="0"/>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="21">
				<Data ss:Type="String">Row</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="21">
				<Data ss:Type="String">End Date</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">Sender ID/MSISDN</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">Receiver ID/MSISDN</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="21">
				<Data ss:Type="String">Amount</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">Transaction Type</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">Transaction Reference</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">Channel</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="21">
				<Data ss:Type="String">Result</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="21">
				<Data ss:Type="String">Sender Balance Before</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="21">
				<Data ss:Type="String">Sender Balance After</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="21">
				<Data ss:Type="String">Receiver Balance Before</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="21">
				<Data ss:Type="String">Receiver Balance After</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">1</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-23T16:23:53.119</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967736955652</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735281814</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">2,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023102316235301401953682</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">51627602.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">49627602.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">83.78</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">2,000,083.78</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">2</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-22T22:17:28.968</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967736955652</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735281814</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023102222172885302978363</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">33337602.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">32337602.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">12.62</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,012.62</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">3</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-21T22:20:49.698</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967735835938</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735281814</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023102122204958901733392</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">1765000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">765000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">54.28</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,054.28</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">4</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-20T18:06:17.901</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967736955652</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735281814</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">2,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023102018061777902604903</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">51886633.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">49886633.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">10.17</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">2,000,010.17</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">5</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-16T16:24:30.329</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967734294667</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735281814</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">4,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101616243022002007294</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">7045239.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">3045239.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">3652101.50</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">7,652,101.5</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">6</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-16T15:38:05.341</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967730663111</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735281814</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">10,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101615380524401012591</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">WEBSERVICE</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">6054492.24</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">6044492.24</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">3722041.17</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">3,732,041.17</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">7</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-15T16:53:27.108</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735281814</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101516532699902859198</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">1500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">0.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">4789845.27</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">6,289,845.27</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">8</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-15T16:51:27.054</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735281814</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">2,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101516512694201888053</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">3500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">1500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">2801215.85</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">4,801,215.85</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">9</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-15T16:50:10.817</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735281814</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">2,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101516501071101887839</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">5500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">3500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">805835.85</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">2,805,835.85</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">10</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-15T15:44:00.428</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735281814</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101515440031402845940</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">6000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">5500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">617938.00</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,117,938</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">11</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-15T01:29:36.242</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735281814</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101501293613602772173</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">7000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">6000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">55.43</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,055.43</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">12</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-14T15:40:42.450</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735281814</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101415404234502690889</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">0.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">70346.36</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">570,346.36</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">13</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-14T12:50:04.811</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735281814</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101412500469902673174</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">2500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">2000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">70.42</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">500,070.42</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">14</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-11T19:23:07.498</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967734294667</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735281814</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">4,500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101119230738402325095</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">38595239.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">34095239.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">1234282.58</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">5,734,282.58</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">15</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-11T19:22:41.394</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967734294667</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735281814</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101119224126801422114</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">39095239.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">38595239.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">734282.58</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,234,282.58</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">16</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-11T18:27:47.602</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735281814</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101118274748602312128</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">5500000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">5000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">234282.58</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">734,282.58</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">17</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-10T15:55:49.690</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735281814</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023101015554957902131337</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">5000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">4000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">18.17</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,018.17</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">18</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-06T20:12:23.330</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735281814</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">1,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100620122322202550852</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">6000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">5000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">140.91</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">1,000,140.91</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">19</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-06T15:06:25.343</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735281814</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100615062518901699039</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">7000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">6500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">34.47</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">500,034.47</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">20</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-05T15:11:04.961</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735281814</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100515110485702367956</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">9000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">8500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">440262.76</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">940,262.76</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">21</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-02T23:22:15.821</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735281814</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">500,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100223221571402951753</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">5000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">4500000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">5000052.79</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">5,500,052.79</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="23">
				<Data ss:Type="Number">22</Data>
			</Cell>
			<Cell ss:Index="2" ss:StyleID="25">
				<Data ss:Type="DateTime">2023-10-02T23:21:50.633</Data>
			</Cell>
			<Cell ss:Index="3" ss:StyleID="21">
				<Data ss:Type="String">967737063794</Data>
			</Cell>
			<Cell ss:Index="4" ss:StyleID="21">
				<Data ss:Type="String">967735281814</Data>
			</Cell>
			<Cell ss:Index="5" ss:StyleID="27">
				<Data ss:Type="String">5,000,000</Data>
			</Cell>
			<Cell ss:Index="6" ss:StyleID="21">
				<Data ss:Type="String">CREDIT_TRANSFER</Data>
			</Cell>
			<Cell ss:Index="7" ss:StyleID="21">
				<Data ss:Type="String">2023100223215052301210651</Data>
			</Cell>
			<Cell ss:Index="8" ss:StyleID="21">
				<Data ss:Type="String">USSD</Data>
			</Cell>
			<Cell ss:Index="9" ss:StyleID="29">
				<Data ss:Type="Number">0</Data>
			</Cell>
			<Cell ss:Index="10" ss:StyleID="31">
				<Data ss:Type="Number">10000000.00</Data>
			</Cell>
			<Cell ss:Index="11" ss:StyleID="31">
				<Data ss:Type="Number">5000000.00</Data>
			</Cell>
			<Cell ss:Index="12" ss:StyleID="31">
				<Data ss:Type="Number">52.79</Data>
			</Cell>
			<Cell ss:Index="13" ss:StyleID="27">
				<Data ss:Type="String">5,000,052.79</Data>
			</Cell>
		</Row>
		<Row ss:AutoFitHeight="1">
			<Cell ss:Index="1" ss:StyleID="32" ss:MergeAcross="13">
				<Data ss:Type="String">Feb 29, 2024, 4:06 PM</Data>
			</Cell>
		</Row>
	</ss:Table>
	<WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">
		<PageSetup>
			<PageMargins x:Bottom="0.25" x:Left="0.25" x:Right="0.25" x:Top="0.25"/>
		</PageSetup>
		<Print>
			<PaperSizeIndex>9</PaperSizeIndex>
		</Print>
	</WorksheetOptions>
</Worksheet>
</Workbook>