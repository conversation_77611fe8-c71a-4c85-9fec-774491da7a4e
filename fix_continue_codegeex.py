#!/usr/bin/env python3
"""
سكريبت لإصلاح وتحسين تكوين Continue.dev مع CodeGeeX2-6B
"""

import json
import os
import shutil
import requests
from pathlib import Path

def backup_config():
    """إنشاء نسخة احتياطية من التكوين الحالي"""
    
    config_path = Path.home() / ".continue" / "config.json"
    backup_path = Path.home() / ".continue" / "config.json.backup"
    
    if config_path.exists():
        shutil.copy2(config_path, backup_path)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
        return True
    else:
        print("⚠️ ملف التكوين غير موجود")
        return False

def create_optimized_config():
    """إنشاء تكوين محسن لـ Continue.dev مع CodeGeeX2-6B"""
    
    config = {
        "models": [
            {
                "title": "CodeGeeX2-6B Local",
                "provider": "openai",
                "model": "THUDM/codegeex2-6b",
                "apiBase": "http://localhost:8001/v1",
                "apiKey": "sk-local-codegeex-key-12345",
                "contextLength": 8192,
                "completionOptions": {
                    "temperature": 0.2,
                    "topP": 0.95,
                    "maxTokens": 1024,
                    "frequencyPenalty": 0.1,
                    "presencePenalty": 0.1
                },
                "systemMessage": "أنت مساعد برمجة ذكي متخصص في كتابة وشرح الكود. أجب بوضوح ودقة."
            }
        ],
        "tabAutocompleteModel": {
            "title": "CodeGeeX2-6B Autocomplete",
            "provider": "openai",
            "model": "THUDM/codegeex2-6b",
            "apiBase": "http://localhost:8001/v1",
            "apiKey": "sk-local-codegeex-key-12345",
            "contextLength": 2048,
            "completionOptions": {
                "temperature": 0.1,
                "maxTokens": 128,
                "stop": ["\n\n", "```", "def ", "class ", "import ", "from "],
                "topP": 0.9
            }
        },
        "customCommands": [
            {
                "name": "شرح الكود",
                "prompt": "اشرح هذا الكود بالعربية بطريقة واضحة ومفصلة، مع ذكر الغرض من كل جزء:\n\n{{{ input }}}\n\nالشرح التفصيلي:"
            },
            {
                "name": "إصلاح الأخطاء",
                "prompt": "راجع هذا الكود وحدد الأخطاء إن وجدت، ثم اقترح الحلول:\n\n{{{ input }}}\n\nتحليل الأخطاء والحلول:"
            },
            {
                "name": "تحسين الكود",
                "prompt": "حسن هذا الكود من ناحية الأداء والوضوح والأمان:\n\n{{{ input }}}\n\nالكود المحسن مع التوضيحات:"
            },
            {
                "name": "إنشاء اختبارات",
                "prompt": "أنشئ اختبارات شاملة لهذا الكود باستخدام أفضل الممارسات:\n\n{{{ input }}}\n\nاختبارات الوحدة:"
            },
            {
                "name": "توثيق الكود",
                "prompt": "أضف توثيقاً شاملاً لهذا الكود باللغة العربية:\n\n{{{ input }}}\n\nالكود مع التوثيق:"
            },
            {
                "name": "تحويل إلى Python",
                "prompt": "حول هذا الكود إلى Python مع الحفاظ على نفس الوظائف:\n\n{{{ input }}}\n\nالكود بـ Python:"
            }
        ],
        "contextProviders": [
            {
                "name": "code",
                "params": {}
            },
            {
                "name": "docs",
                "params": {}
            },
            {
                "name": "diff",
                "params": {}
            },
            {
                "name": "terminal",
                "params": {}
            },
            {
                "name": "problems",
                "params": {}
            },
            {
                "name": "folder",
                "params": {}
            },
            {
                "name": "codebase",
                "params": {}
            },
            {
                "name": "git",
                "params": {}
            }
        ],
        "slashCommands": [
            {
                "name": "edit",
                "description": "تعديل الكود المحدد"
            },
            {
                "name": "comment",
                "description": "إضافة تعليقات للكود"
            },
            {
                "name": "share",
                "description": "مشاركة المحادثة"
            },
            {
                "name": "cmd",
                "description": "تشغيل أمر في الطرفية"
            },
            {
                "name": "commit",
                "description": "إنشاء رسالة commit"
            }
        ],
        "allowAnonymousTelemetry": False,
        "embeddingsProvider": {
            "provider": "transformers.js",
            "model": "Xenova/all-MiniLM-L6-v2"
        },
        "reranker": {
            "name": "free-trial"
        },
        "ui": {
            "fontSize": 14,
            "displayRawMarkdown": False
        },
        "experimental": {
            "useChromiumForDocsCrawling": True,
            "useTreeSitter": True
        }
    }
    
    return config

def test_server_connection():
    """اختبار الاتصال مع الخادم"""
    
    try:
        response = requests.get("http://localhost:8001/v1/models", timeout=5)
        if response.status_code == 200:
            models = response.json()
            print("✅ الخادم متصل ويعمل بشكل صحيح")
            print(f"📋 النماذج المتاحة: {[m['id'] for m in models.get('data', [])]}")
            return True
        else:
            print(f"❌ خطأ في الاتصال: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ فشل الاتصال مع الخادم: {e}")
        return False

def create_startup_script():
    """إنشاء سكريبت لبدء تشغيل الخادم"""
    
    script_content = """#!/bin/bash
# سكريبت بدء تشغيل خادم CodeGeeX2-6B

echo "🚀 بدء تشغيل خادم CodeGeeX2-6B..."

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 غير مثبت"
    exit 1
fi

# التحقق من المنفذ
if lsof -Pi :8001 -sTCP:LISTEN -t >/dev/null ; then
    echo "✅ الخادم يعمل بالفعل على المنفذ 8001"
else
    echo "🔄 بدء تشغيل الخادم..."
    # هنا يجب إضافة الأمر الصحيح لبدء تشغيل خادم CodeGeeX2-6B
    # مثال: python3 -m vllm.entrypoints.openai.api_server --model THUDM/codegeex2-6b --port 8001
    echo "⚠️ يرجى تشغيل خادم CodeGeeX2-6B يدوياً على المنفذ 8001"
fi

echo "✅ جاهز للاستخدام!"
"""
    
    script_path = Path.home() / "start_codegeex_server.sh"
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    # جعل السكريبت قابل للتنفيذ
    os.chmod(script_path, 0o755)
    print(f"✅ تم إنشاء سكريبت البدء: {script_path}")

def main():
    """الدالة الرئيسية"""
    
    print("🔧 إصلاح وتحسين تكوين Continue.dev مع CodeGeeX2-6B")
    print("=" * 60)
    
    # إنشاء مجلد Continue إذا لم يكن موجوداً
    continue_dir = Path.home() / ".continue"
    continue_dir.mkdir(exist_ok=True)
    
    # إنشاء نسخة احتياطية
    backup_config()
    
    # اختبار الاتصال مع الخادم
    server_ok = test_server_connection()
    
    # إنشاء التكوين المحسن
    print("\n🔧 إنشاء التكوين المحسن...")
    config = create_optimized_config()
    
    config_path = continue_dir / "config.json"
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ تم حفظ التكوين المحسن: {config_path}")
    
    # إنشاء سكريبت البدء
    create_startup_script()
    
    # إنشاء ملف .continueignore إذا لم يكن موجوداً
    continueignore_path = continue_dir / ".continueignore"
    if not continueignore_path.exists():
        ignore_content = """# ملفات يجب تجاهلها من قبل Continue.dev
node_modules/
.git/
__pycache__/
*.pyc
.env
.venv/
venv/
.DS_Store
*.log
.cache/
dist/
build/
"""
        with open(continueignore_path, 'w', encoding='utf-8') as f:
            f.write(ignore_content)
        print(f"✅ تم إنشاء ملف .continueignore")
    
    print("\n" + "=" * 60)
    print("🎉 تم إصلاح وتحسين التكوين بنجاح!")
    
    if server_ok:
        print("✅ النظام جاهز للاستخدام")
        print("💡 يمكنك الآن استخدام Continue.dev في VS Code")
    else:
        print("⚠️ تأكد من تشغيل خادم CodeGeeX2-6B على المنفذ 8001")
        print("🔧 استخدم السكريبت: ~/start_codegeex_server.sh")
    
    print("\n📋 الميزات الجديدة:")
    print("   • أوامر مخصصة محسنة باللغة العربية")
    print("   • إعدادات محسنة للأداء")
    print("   • دعم أفضل للإكمال التلقائي")
    print("   • مزودي سياق إضافيين")

if __name__ == "__main__":
    main()
