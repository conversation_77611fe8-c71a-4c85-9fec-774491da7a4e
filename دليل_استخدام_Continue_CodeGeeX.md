# دليل استخدام Continue.dev مع CodeGeeX2-6B المحلي

## 📋 نظرة عامة

تم إصلاح وتحسين تكوين Continue.dev للعمل مع نموذج CodeGeeX2-6B المحمل محلياً. النظام الآن جاهز للاستخدام مع ميزات محسنة ودعم كامل للغة العربية.

## ✅ ما تم إصلاحه

### 1. تصحيح اسم النموذج
- **قبل**: `"model": "codegeex2-6b"`
- **بعد**: `"model": "THUDM/codegeex2-6b"`

### 2. تحسين إعدادات الأداء
- زيادة `maxTokens` إلى 1024 للنموذج الرئيسي
- إضافة `frequencyPenalty` و `presencePenalty`
- تحسين إعدادات `stop` للإكمال التلقائي

### 3. أوا<PERSON>ر مخصصة محسنة
- شرح الكود (باللغة العربية)
- إصلاح الأخطاء
- تحسين الكود
- إنشاء اختبارات
- توثيق الكود
- تحويل إلى Python

### 4. مزودي سياق إضافيين
- دعم Git
- تحسين دعم المجلدات
- دعم أفضل للمشاكل والأخطاء

## 🚀 كيفية الاستخدام

### في VS Code

1. **تأكد من تشغيل الخادم**:
   ```bash
   # تحقق من حالة الخادم
   curl http://localhost:8001/v1/models
   ```

2. **افتح Continue.dev**:
   - اضغط `Ctrl+Shift+P` (أو `Cmd+Shift+P` على Mac)
   - ابحث عن "Continue"
   - أو استخدم الاختصار `Ctrl+I`

3. **استخدم الأوامر المخصصة**:
   - `/شرح الكود` - لشرح الكود المحدد
   - `/إصلاح الأخطاء` - لإيجاد وإصلاح الأخطاء
   - `/تحسين الكود` - لتحسين الكود
   - `/إنشاء اختبارات` - لإنشاء اختبارات الوحدة
   - `/توثيق الكود` - لإضافة التوثيق
   - `/تحويل إلى Python` - لتحويل الكود إلى Python

### الإكمال التلقائي

- يعمل تلقائياً أثناء الكتابة
- اضغط `Tab` لقبول الاقتراح
- اضغط `Esc` لرفض الاقتراح

## 🔧 استكشاف الأخطاء

### مشكلة: النموذج لا يستجيب

**الحل**:
```bash
# تحقق من حالة الخادم
curl http://localhost:8001/v1/models

# إذا لم يعمل، أعد تشغيل الخادم
~/start_codegeex_server.sh
```

### مشكلة: رسائل خطأ في Continue.dev

**الحل**:
1. تحقق من ملف التكوين:
   ```bash
   cat ~/.continue/config.json
   ```

2. أعد تشغيل VS Code

3. تحقق من سجلات Continue.dev في VS Code

### مشكلة: الإكمال التلقائي بطيء

**الحل**:
- قلل `maxTokens` في إعدادات `tabAutocompleteModel`
- قلل `contextLength`
- تأكد من أن الخادم يعمل على نفس الجهاز

## 📊 مراقبة الأداء

### اختبار الاتصال
```bash
python3 test_codegeex_connection.py
```

### مراقبة استخدام الذاكرة
```bash
# مراقبة استخدام GPU (إذا كان متاحاً)
nvidia-smi

# مراقبة استخدام CPU والذاكرة
htop
```

## ⚙️ تخصيص الإعدادات

### تعديل درجة الحرارة (Temperature)
```json
{
  "completionOptions": {
    "temperature": 0.1  // للكود الدقيق
    // أو
    "temperature": 0.7  // للنصوص الإبداعية
  }
}
```

### إضافة أوامر مخصصة جديدة
```json
{
  "customCommands": [
    {
      "name": "اسم الأمر",
      "prompt": "وصف المهمة:\n\n{{{ input }}}\n\nالنتيجة:"
    }
  ]
}
```

## 🔒 الأمان والخصوصية

- ✅ جميع البيانات تبقى محلية
- ✅ لا يتم إرسال الكود إلى خوادم خارجية
- ✅ تم تعطيل التتبع (`allowAnonymousTelemetry: false`)

## 📁 ملفات النظام

```
~/.continue/
├── config.json              # التكوين الرئيسي
├── config.json.backup       # نسخة احتياطية
├── .continueignore          # ملفات يجب تجاهلها
└── sessions/                # سجل المحادثات
```

## 🆘 الدعم والمساعدة

### إذا واجهت مشاكل:

1. **تحقق من السجلات**:
   - سجلات VS Code: `Help > Toggle Developer Tools > Console`
   - سجلات Continue.dev في لوحة الإخراج

2. **أعد تشغيل النظام**:
   ```bash
   # أعد تشغيل الخادم
   pkill -f codegeex
   ~/start_codegeex_server.sh
   
   # أعد تشغيل VS Code
   ```

3. **استعد التكوين الافتراضي**:
   ```bash
   python3 fix_continue_codegeex.py
   ```

## 🎯 نصائح للاستخدام الأمثل

1. **استخدم أوصاف واضحة** عند طلب المساعدة
2. **حدد الكود** قبل استخدام الأوامر المخصصة
3. **استخدم السياق** - Continue.dev يفهم ملفات المشروع
4. **جرب درجات حرارة مختلفة** حسب نوع المهمة

---

**تم إعداد النظام بنجاح! 🎉**

يمكنك الآن الاستمتاع بمساعد الذكاء الاصطناعي المحلي للبرمجة.
