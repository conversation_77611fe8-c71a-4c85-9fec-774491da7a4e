#!/usr/bin/env python3
"""
فحص سريع لحالة Continue.dev في Windsurf
"""

import os
import subprocess
import requests
import json
from pathlib import Path

def quick_check():
    """فحص سريع للنظام"""
    
    print("🔍 فحص سريع لحالة Continue.dev في Windsurf")
    print("=" * 50)
    
    checks = []
    
    # 1. فحص Windsurf
    try:
        result = subprocess.run(['which', 'windsurf'], capture_output=True, text=True)
        if result.returncode == 0:
            checks.append(("Windsurf IDE", "✅ مثبت", result.stdout.strip()))
        else:
            checks.append(("Windsurf IDE", "❌ غير مثبت", ""))
    except:
        checks.append(("Windsurf IDE", "❌ غير متاح", ""))
    
    # 2. فحص Continue.dev extension
    ext_dirs = [
        "~/.vscode/extensions",
        "~/.windsurf/extensions",
        "~/.config/windsurf/extensions"
    ]
    
    continue_found = False
    for ext_dir in ext_dirs:
        ext_path = os.path.expanduser(ext_dir)
        if os.path.exists(ext_path):
            for item in os.listdir(ext_path):
                if "continue" in item.lower():
                    checks.append(("Continue.dev Extension", "✅ مثبت", f"{ext_path}/{item}"))
                    continue_found = True
                    break
            if continue_found:
                break
    
    if not continue_found:
        checks.append(("Continue.dev Extension", "❌ غير مثبت", ""))
    
    # 3. فحص ملف التكوين
    config_path = os.path.expanduser("~/.continue/config.json")
    if os.path.exists(config_path):
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            models = config.get("models", [])
            if models:
                checks.append(("ملف التكوين", "✅ صحيح", f"{len(models)} نموذج"))
            else:
                checks.append(("ملف التكوين", "⚠️ فارغ", "لا توجد نماذج"))
        except:
            checks.append(("ملف التكوين", "❌ تالف", ""))
    else:
        checks.append(("ملف التكوين", "❌ غير موجود", ""))
    
    # 4. فحص الخادم المحلي
    try:
        response = requests.get("http://localhost:8001/v1/models", timeout=3)
        if response.status_code == 200:
            models = response.json().get("data", [])
            checks.append(("الخادم المحلي", "✅ يعمل", f"{len(models)} نموذج"))
        else:
            checks.append(("الخادم المحلي", "❌ خطأ", f"كود: {response.status_code}"))
    except:
        checks.append(("الخادم المحلي", "❌ لا يعمل", "المنفذ 8001"))
    
    # عرض النتائج
    print("\n📊 نتائج الفحص:")
    for name, status, details in checks:
        print(f"   {status} {name}")
        if details:
            print(f"      📝 {details}")
    
    # تقييم عام
    success_count = sum(1 for _, status, _ in checks if "✅" in status)
    total_count = len(checks)
    
    print(f"\n📈 النتيجة: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 ممتاز! كل شيء يعمل بشكل مثالي")
        print("💡 افتح Windsurf واضغط Ctrl+I")
        return True
    elif success_count >= total_count - 1:
        print("✅ جيد! النظام يعمل مع مشكلة بسيطة")
        print("🔧 راجع التفاصيل أعلاه")
        return True
    else:
        print("⚠️ يحتاج إصلاح! هناك مشاكل متعددة")
        print("🔧 شغل: python3 install_continue_windsurf.py")
        return False

def show_usage_tips():
    """نصائح الاستخدام"""
    
    print("\n💡 نصائح سريعة:")
    print("   • اضغط Ctrl+I لفتح Continue.dev")
    print("   • استخدم /شرح الكود لشرح الكود المحدد")
    print("   • استخدم /إصلاح الأخطاء لإصلاح المشاكل")
    print("   • اكتب أسئلة مباشرة مثل 'كيف أنشئ دالة؟'")

def main():
    """الدالة الرئيسية"""
    
    success = quick_check()
    
    if success:
        show_usage_tips()
    else:
        print("\n🔧 خطوات الإصلاح:")
        print("   1. python3 install_continue_windsurf.py")
        print("   2. أعد تشغيل Windsurf")
        print("   3. python3 check_windsurf_continue.py")
    
    print(f"\n📖 راجع الدليل: دليل_Windsurf_Continue.md")

if __name__ == "__main__":
    main()
