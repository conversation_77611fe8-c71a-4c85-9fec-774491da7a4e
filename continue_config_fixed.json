{"models": [{"title": "CodeGeeX2-6B Local", "provider": "openai", "model": "THUDM/codegeex2-6b", "apiBase": "http://localhost:8001/v1", "apiKey": "sk-local-codegeex-key-12345", "contextLength": 8192, "completionOptions": {"temperature": 0.2, "topP": 0.95, "maxTokens": 512}}], "tabAutocompleteModel": {"title": "CodeGeeX2-6B Local Autocomplete", "provider": "openai", "model": "THUDM/codegeex2-6b", "apiBase": "http://localhost:8001/v1", "apiKey": "sk-local-codegeex-key-12345", "contextLength": 2048, "completionOptions": {"temperature": 0.1, "maxTokens": 128, "stop": ["\n\n", "```"]}}, "customCommands": [{"name": "شر<PERSON> الكود", "prompt": "اشرح هذا الكود بالعربية بطريقة واضحة ومفصلة:\n\n{{{ input }}}\n\nالشرح:"}, {"name": "إصلاح الأخطاء", "prompt": "هناك خطأ في هذا الكود. اشرح المشكلة واقترح الحل:\n\n{{{ input }}}\n\nالمشكلة والحل:"}, {"name": "إنشاء اختبارات", "prompt": "أنشئ اختبارات وحدة لهذا الكود:\n\n{{{ input }}}\n\nالاختبارات:"}, {"name": "تحسين الكود", "prompt": "حسن هذا الكود من ناحية الأداء والوضوح:\n\n{{{ input }}}\n\nالكود المحسن:"}], "contextProviders": [{"name": "code", "params": {}}, {"name": "docs", "params": {}}, {"name": "diff", "params": {}}, {"name": "terminal", "params": {}}, {"name": "problems", "params": {}}, {"name": "folder", "params": {}}, {"name": "codebase", "params": {}}], "slashCommands": [{"name": "edit", "description": "تعديل الكود المحدد"}, {"name": "comment", "description": "إضافة تعليقات للكود"}, {"name": "share", "description": "مشاركة المحادثة"}, {"name": "cmd", "description": "تشغيل أمر في الطرفية"}], "allowAnonymousTelemetry": false, "embeddingsProvider": {"provider": "transformers.js"}}